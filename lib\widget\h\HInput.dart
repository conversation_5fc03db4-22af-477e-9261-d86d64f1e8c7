import 'package:flutter/material.dart';
import 'package:flutter_starry_sky_box/base/styles/HTheme.dart';
import 'package:flutter_starry_sky_box/widget/screenutil/custom_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class HInput extends StatefulWidget {

  final (String? img, String? title) left;
  final Widget? right;
  final (bool showInput, bool inputLeft, String? hitStr) allBool ;
  final double hieght;
  final void Function(String n)? onChanged;

  const HInput({super.key, this.left = (null,null), this.right, this.allBool = (true,false,null), this.hieght = 56, this.onChanged});


  @override
  State<StatefulWidget> createState() => _HInput();
}


class _HInput extends State<HInput> {

  late TextEditingController textEditingController = TextEditingController();


  @override
  Widget build(BuildContext context) => Container(
    height: widget.hieght,
    padding: EdgeInsets.symmetric(horizontal: 12),
    decoration: FltBox.box(
      bgColor: Colors.white,radius: 12,
    ),
    child: Row(
      children: [
        if (widget.left.$1 != null)
          SvgPicture.asset(widget.left.$1!,height: 30,),
        if (widget.left.$2 != null)
          Text(widget.left.$2!,style: FltTs.ts(size: 13,weight: 5),),
        Expanded(
          child: widget.allBool.$1 ? Padding(
            padding: EdgeInsets.symmetric(horizontal: 4),
            child: TextField(
              controller: textEditingController, keyboardType: TextInputType.number,
              decoration: InputDecoration(
                border: InputBorder.none, hintText: widget.allBool.$3 ?? "填写"+(widget.left.$2 ?? ""),
                hintStyle: FltTs.ts(color: Colors.black38,weight: 5,size: 13),
              ),
              textAlign: widget.allBool.$2 ? TextAlign.left : TextAlign.right,
              onChanged: widget.onChanged,
            ),
          ) : Container(),
        ),
        if (widget.right != null)
          widget.right!
      ],
    ),
  );

}