import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flustars_flutter3/flustars_flutter3.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_starry_sky_box/api/api_response.dart';
import 'package:flutter_starry_sky_box/api/req/user/req_user.dart';
import 'package:flutter_starry_sky_box/pages/login/third_login.dart';
import 'package:flutter_starry_sky_box/pages/main/my/user_store.dart';
import 'package:flutter_starry_sky_box/pages/register/register_controller.dart';
import 'package:flutter_starry_sky_box/routers/app_routes.dart';
import 'package:flutter_starry_sky_box/utils/app_utils.dart';
import 'package:flutter_starry_sky_box/utils/custom_constant.dart';
import 'package:flutter_starry_sky_box/utils/custom_event_util.dart';
import 'package:flutter_starry_sky_box/utils/custom_logger.dart';
import 'package:flutter_starry_sky_box/utils/regex/regex_util.dart';
import 'package:flutter_starry_sky_box/utils/str_utils.dart';
import 'package:flutter_starry_sky_box/widget/dialog/login_xy_dialog.dart';
import 'package:flutter_starry_sky_box/widget/toast/custom_toast.dart';
import 'package:get/get.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:video_player/video_player.dart';

import '../../global.dart';
import '../../utils/adjust_utils.dart';
import '../../utils/messaging_util.dart';
import '../main/main_controller.dart';
import '../splash/splash_controller.dart';

class LoginController extends GetxController with GetTickerProviderStateMixin {
  TabController? tabController;

  @override
  void onClose() {
    logger.d('关闭登录页面');
    eventBus.fire(const BottomNavigationBarEvent(CustomConstant.PLAY_VIDEO));
    super.onClose();
    videoPlayerController.dispose();
  }

  late VideoPlayerController videoPlayerController;

  String? agentCode;

  @override
  void onInit() {
    super.onInit();
    logger.i('获取渠道邀请码');
    Global.openinstallFlutterPlugin.install(installHandler);

    Future.delayed(Duration.zero, () async {
      if (Platform.isAndroid) {
        await Permission.notification.request();
      }
    });
    UserStore.to.homeGetConfig();

    eventBus.fire(const BottomNavigationBarEvent(CustomConstant.STOP_VIDEO));
    tabController = TabController(length: 2, vsync: this, initialIndex: 0);

    eventBus.on<BottomNavigationBarEvent>().listen((event) {
      if (event.message == CustomConstant.LANG_REFRESH) {
        emailAutoCodeText = 'get_verification_code'.tr;
        phoneAutoCodeText = 'get_verification_code'.tr;
        update();
      }
    });

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      initLogin();
    });

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      videoPlayerController =
          VideoPlayerController.asset('assets/video/may8_compressed_1.mp4')
            ..initialize().then((_) {
              isShouwVideo = true;
              videoPlayerController.play();
              update();
            });

      videoPlayerController.setLooping(true);
    });
  }

  bool isShouwVideo = false;

  initLogin({int? p}) {
    bool isRem = SpUtil.getBool('remember') ?? false;
    if (isRem) {
      if (p != null) {
        int lType1 = SpUtil.getInt('loginType') ?? 0;
        if (lType1 != p) {
          return;
        }
      }

      bool codeLog = SpUtil.getBool("codeLogin") ?? false;

      userLoginController.text = SpUtil.getString('userLogin') ?? '';
      if (codeLog == false) {
        pwdController.text = SpUtil.getString('code') ?? '';
      }

      int lType = SpUtil.getInt('loginType') ?? 0;
      if (lType == 1) {
        countryCode = SpUtil.getString('countryCode') ?? '62';
      }

      loginType = lType;

      isRemember = isRem;

      tabController!.index = loginType;
      isSubmit = true;
      isClearInput = true;
    }

    update();
  }

  // 是否验证码登录
  bool codeLogin = false;

  changeCodeLogin() {
    codeLogin = !codeLogin;

    pwdController.clear();
    update();
  }

  // 登录方式
  int loginType = 0;

  changeLoginType(p) {
    loginType = p;

    isClearInput = false;

    userLoginController.clear();
    pwdController.clear();

    update();
    // checkLoginSubmit();

    initLogin(p: p);
  }

  // 区号
  late String countryCode = setDefaultCountry();

  changeCountryCode(p) {
    countryCode = p;
    update();
  }

  setDefaultCountry() {
    return StrUtils.getLang() == 'id_ID'
        ? '62'
        : StrUtils.getLang() == 'zh_CN'
            ? '86'
            : '1';
  }

  // 账号
  TextEditingController userLoginController =
      TextEditingController(text: kDebugMode ? "<EMAIL>" : "");

  // 密码
  TextEditingController pwdController =
      TextEditingController(text: kDebugMode ? "888999uio" : "");

  bool isLogin = false;

  // 登录
  login() async {
    if (loginType == 0) {
      // 邮箱登录
      if (userLoginController.text.trim() == '') {
        CustomToast.showTextToast('please_enter_your_email_address'.tr);
        return;
      }
    } else if (loginType == 1) {
      // 手机登录
      if (userLoginController.text.trim() == '') {
        CustomToast.showTextToast('please_enter_your_phone_number'.tr);
        return;
      }
    }
    if (codeLogin) {
      // 验证码登录
      if (pwdController.text.trim() == '') {
        CustomToast.showTextToast('please_enter_the_verification_code'.tr);
        return;
      }
    } else {
      // 密码登录
      if (pwdController.text.trim() == '') {
        CustomToast.showTextToast('please_enter_your_password'.tr);
        return;
      }
    }

    if (isAgreed == false) {
      // CustomToast.showTextToast('please_agree_to_the_user_agreement_and_privacy_policy'.tr);
      LoginXyDialog.show(Get.context!, confirmTap: () {
        Get.back();
        isAgreed = true;
        update();
        login();
      });
      return;
    }

    if (isLogin == true) {
      return;
    }

    try {
      isLogin = true;
      EasyLoading.show(status: '${'logging_in'.tr}...');

      var resp = await ReqUser.userLogin2(
          loginType: codeLogin ? 'code' : 'pwd',
          type: loginType == 0 ? 'email' : 'phone',
          countryCode: loginType == 0 ? '1' : countryCode ?? '',
          userLogin: userLoginController.text,
          code: pwdController.text);
      var data = resp.data['data'];
      if (resp.status == Status.completed && data != null) {
        if (resp.data['data']['code'] == 0) {
          // UserStore.to.setLoginUserInfo(resp.data['data']['info'][0]);
          AppUtils.setUidToken(
              uid: resp.data['data']['info'][0]['id'],
              token: resp.data['data']['info'][0]['token']);

          // 发送Adjust事件
          AdJustUtils.trackEvent('登录成功');

          if (isRemember == true) {
            SpUtil.putBool("remember", isRemember);
            SpUtil.putInt("loginType", loginType);
            SpUtil.putString("countryCode", countryCode ?? '');
            SpUtil.putString("userLogin", userLoginController.text);
            SpUtil.putString("code", pwdController.text);
            SpUtil.putBool("codeLogin", codeLogin);
          } else {
            SpUtil.remove('remember');
            SpUtil.remove('loginType');
            SpUtil.remove('countryCode');
            SpUtil.remove('userLogin');
            SpUtil.remove('code');
            SpUtil.remove('codeLogin');
          }
          //
          // MessagingUtil.initFCM();
          CustomToast.showTextToast('login_successful'.tr);
          //更新信息-暂时
          if (Get.isRegistered<MainController>()) {
            // MainController.to.homeGetConfig();
            // UserStore.to.getBaseInfo();
            Get.back();
            eventBus.fire(
                const BottomNavigationBarEvent(CustomConstant.LOGIN_REFRESH));
          } else {
            eventBus.fire(
                const BottomNavigationBarEvent(CustomConstant.LOGIN_REFRESH));
            Get.offAllNamed(AppRoutes.main);
          }
          videoPlayerController.dispose();
          // Get.offAllNamed(AppRoutes.main);
          isLogin = false;
        } else {
          CustomToast.showTextToast('${resp.data['data']['msg']}');
          isLogin = false;
        }
      } else {
        CustomToast.showTextToast(
            '${resp.data['data']['msg'] ?? 'the_system_is_busy_please_try_again_later'.tr}');
        isLogin = false;
      }
    } catch (e) {
      logger.d('====$e');
      CustomToast.showTextToast('the_system_is_busy_please_try_again_later'.tr);
      isLogin = false;
    }
  }

  // 是否可以登录
  bool isSubmit = false;

  bool isClearInput = false;

  chearInput() {
    isClearInput = false;
    userLoginController.clear();
    update();
  }

  int selectedThirdPartyIndex = 0;

  setSelectedThirdPartyIndex(int index) {
    selectedThirdPartyIndex = index;
    update();
  }

  Future installHandler(Map<String, dynamic> data) async {
    logger.i('data => $data');
    try {
      if (data.containsKey('bindData')) {
        if (jsonDecode(data['bindData']).containsKey('invite_code')) {
          agentCode = jsonDecode(data['bindData'])['invite_code'];
          logger.i('邀请码：$agentCode');
        }
      }
    } catch (e) {
      logger.e('获取邀请码异常$e');
    }
  }

  checkLoginSubmit() {
    if (loginType == 0) {
      // 邮箱登录
      if (userLoginController.text == '') {
        isSubmit = false;
        isClearInput = false;
        update();
        return;
      } else {
        isClearInput = true;
        update();
      }
    } else if (loginType == 1) {
      // 手机登录
      if (userLoginController.text == '') {
        isSubmit = false;
        isClearInput = false;
        update();
        return;
      } else {
        isClearInput = true;
        update();
      }
    }

    if (codeLogin) {
      // 验证码登录
      if (pwdController.text.trim() == '') {
        isSubmit = false;
        update();
        return;
      }
    } else {
      // 密码登录
      if (pwdController.text.trim() == '') {
        isSubmit = false;
        update();
        return;
      }
    }
    isSubmit = true;
    update();
  }

  bool showPass = false;

  changeShowPass() {
    showPass = !showPass;
    update();
  }

  /// 是否记住密码
  bool isRemember = false;

  changeRemember() {
    isRemember = !isRemember;
    update();
  }

  bool isAgreed = false;

  changeIsAgreed() {
    isAgreed = !isAgreed;
    update();
  }

  // 获取验证码
  Timer? emailTimer;
  int emailTimeCount = 60;
  String emailAutoCodeText = 'get_verification_code'.tr;

  /// 是否发送验证码
  bool emailIsSendCode = false;

  @override
  void dispose() {
    super.dispose();
    emailTimer?.cancel();
    phoneTimer?.cancel();
  }

  /// 验证码倒计时
  void startEmailTimer() {
    emailTimer?.cancel();
    emailTimeCount = 60;
    emailIsSendCode = true;
    emailAutoCodeText = "${'resend'.tr}(${emailTimeCount}s)";
    update();
    emailTimer = Timer.periodic(const Duration(seconds: 1), (Timer timer) {
      if (emailTimeCount <= 1) {
        emailAutoCodeText = 'resend'.tr;
        emailTimer?.cancel();
        emailTimeCount = 60;
        emailIsSendCode = false;
        update();
      } else {
        emailTimeCount -= 1;
        emailAutoCodeText = "${'resend'.tr}(${emailTimeCount}s)";
        emailIsSendCode = true;
        update();
      }
    });
  }

  // 获取验证吗
  sendEmailCode() async {
    if (emailIsSendCode == true) {
      return;
    }
    if (!CustomRegexUtil.isEmail(userLoginController.text.trim())) {
      CustomToast.showTextToast('please_enter_a_valid_email_address'.tr);
      return;
    }
    try {
      EasyLoading.show(status: '${'sending'.tr}...');

      var resp =
          await ReqUser.loginGetEmailCode(email: userLoginController.text);

      if (resp.status == Status.completed) {
        if (resp.data['data']['code'] == 0 ||
            resp.data['data']['code'] == 667) {
          EasyLoading.showToast(
              'the_verification_code_has_been_sent_to_your_email_please_check_it'
                  .tr);
          startEmailTimer();
        } else {
          CustomToast.showTextToast('${resp.data['data']['msg']}');
        }
      } else {
        CustomToast.showTextToast(
            '${resp.data['data']['msg'] ?? 'the_system_is_busy_please_try_again_later'.tr}');
      }
    } catch (e) {
      CustomToast.showTextToast('the_system_is_busy_please_try_again_later'.tr);
    }
  }

  // 获取验证码
  Timer? phoneTimer;
  int phoneTimeCount = 60;
  String phoneAutoCodeText = 'get_verification_code'.tr;

  /// 是否发送验证码
  bool phoneIsSendCode = false;

  /// 验证码倒计时
  void startPhoneTimer() {
    phoneTimer?.cancel();
    phoneTimeCount = 60;
    phoneIsSendCode = true;
    phoneAutoCodeText = "${'resend'.tr}(${phoneTimeCount}s)";
    update();
    phoneTimer = Timer.periodic(const Duration(seconds: 1), (Timer timer) {
      if (phoneTimeCount <= 1) {
        phoneAutoCodeText = 'resend'.tr;
        phoneTimer?.cancel();
        phoneTimeCount = 60;
        phoneIsSendCode = false;
        update();
      } else {
        phoneTimeCount -= 1;
        phoneAutoCodeText = "${'resend'.tr}(${phoneTimeCount}s)";
        phoneIsSendCode = true;
        update();
      }
    });
  }

  // 获取验证吗
  sendPhoneCode() async {
    if (phoneIsSendCode == true) {
      return;
    }

    if (userLoginController.text.trim() == '') {
      EasyLoading.showToast('please_enter_your_mobile_number'.tr);
      return;
    }

    try {
      EasyLoading.show(status: '${'sending'.tr}...');
      var resp = await ReqUser.loginGetLoginCode(
          mobile: userLoginController.text, countryCode: countryCode ?? '62');

      if (resp.status == Status.completed) {
        if (resp.data['data']['code'] == 0 ||
            resp.data['data']['code'] == 667) {
          EasyLoading.showToast(
              'the_verification_code_has_been_sent_to_your_mobile_phone_please_check_it'
                  .tr);
          startPhoneTimer();
        } else {
          CustomToast.showTextToast('${resp.data['data']['msg']}');
        }
      } else {
        CustomToast.showTextToast(
            '${resp.data['data']['msg'] ?? 'the_system_is_busy_please_try_again_later'.tr}');
      }
    } catch (e) {
      CustomToast.showTextToast('the_system_is_busy_please_try_again_later'.tr);
    } finally {
      EasyLoading.dismiss();
    }
  }

  // ---------------------- google login begin --------------------------------
  // GoogleSignIn googleSignIn = GoogleSignIn(
  //   // Optional clientId
  //   // clientId: 'your-client_id.apps.googleusercontent.com',
  //   scopes: [
  //     'email',
  //     'https://www.googleapis.com/auth/contacts.readonly',
  //   ],
  // );

  // googleLogin() async {
  //   if (isAgreed == false) {
  //     CustomToast.showTextToast(
  //         'please_agree_to_the_user_agreement_and_privacy_policy'.tr);
  //     return;
  //   }

  //   try {
  //     GoogleSignInAccount? googleUser = await googleSignIn.signIn();
  //     logger.d("googleUser:${googleUser.toString()}");
  //     if (googleUser == null) {
  //       CustomToast.showTextToast('login_failed'.tr);
  //       return;
  //     } else {
  //       // googleUser.authentication;
  //       // Obtain the auth details from the request
  //       EasyLoading.show(status: '${'logging_in'.tr}...');
  //       GoogleSignInAuthentication signInAuthentication =
  //           (await googleUser.authentication);
  //       // signInAuthentication.accessToken;

  //       thirdLogin(signInAuthentication.accessToken ?? '');
  //     }
  //   } catch (e) {
  //     logger.d('=========${e}');
  //     CustomToast.showTextToast('$e');
  //   }
  // }

  thirdLogin({
    required String accessToken,
    required String utmSource,
    String? niceName,
    String? avatar,
  }) async {
    try {
      var resp = await ReqUser.userLoginByThird2(
        utmSource: utmSource,
        accessToken: accessToken,
        niceName: niceName,
        avatar: avatar,
        agentCode: agentCode,
      );

      if (resp.status == Status.completed) {
        if (resp.data['data']['code'] == 0) {
          AppUtils.setUidToken(
              uid: resp.data['data']['info'][0]['id'],
              token: resp.data['data']['info'][0]['token']);
          CustomToast.showTextToast('login_successful'.tr);

          // 发送Adjust事件
          AdJustUtils.trackEvent('三方登录成功');

          if (agentCode != null && agentCode != '') {
            // 邀请登录
            AdJustUtils.trackEvent('被邀请人完成注册：$agentCode');
          }

          UserStore.to.getBaseInfo(isLogin: true);

          Get.offAllNamed(AppRoutes.main);
        } else {
          CustomToast.showTextToast('${resp.data['data']['msg']}');
        }
      } else {
        ThirdLoginAndLogout.logout();
        CustomToast.showTextToast(
            '${resp.data['data']['msg'] ?? 'the_system_is_busy_please_try_again_later'.tr}');
      }
    } catch (e) {
      ThirdLoginAndLogout.logout();
      CustomToast.showTextToast(
          '${'the_system_is_busy_please_try_again_later'.tr}-$e');
    }
  }
}
