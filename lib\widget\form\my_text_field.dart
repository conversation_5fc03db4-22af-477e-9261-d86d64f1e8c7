import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_starry_sky_box/widget/base/ys_tik_tok.dart';
import 'package:flutter_starry_sky_box/widget/screenutil/custom_screenutil.dart';
import 'package:flutter_starry_sky_box/widget/toast/custom_toast.dart';
import 'package:get/get.dart';

///MyTextField
class MyTextField extends StatefulWidget {
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final InputDecoration? decoration;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final double? height;
  final EdgeInsetsGeometry? margin;
  final ValueChanged<String>? onSubmitted;
  final bool obscureText;
  final AssetImage? prefixImage;
  final bool autofocus;
  final bool readOnly;
  final GestureTapCallback? onTap;
  final VoidCallback? onClear; // 新增 onClear 参数
  final VoidCallback? onEditingComplete;
  final TextStyle? style;
  final double iconSize;
  final double? radius;
  final List<TextInputFormatter>? textInputFormatter;

  ///构造函数
  const MyTextField({
    Key? key,
    this.height,
    this.margin,
    this.controller,
    this.focusNode,
    this.keyboardType,
    this.textInputAction,
    this.decoration,
    this.onSubmitted,
    this.obscureText = false,
    this.prefixImage,
    this.textInputFormatter,
    this.autofocus = false,
    this.readOnly = false,
    this.onTap,
    this.onClear,
    this.onEditingComplete,
    this.style,
    this.iconSize = 24,
    this.radius,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => _MyTextFieldState();
}

class _MyTextFieldState extends State<MyTextField> {
  static InputBorder _enabledBorder = OutlineInputBorder(
    borderRadius: BorderRadius.circular(21.r),
    borderSide: BorderSide(
      color: const Color(0xFFC8C8CD),
      width: 0.4.w,
    ),
  );
  static InputBorder _focusedBorder = OutlineInputBorder(
      borderRadius: BorderRadius.circular(21.r), borderSide: BorderSide.none
      // borderSide: BorderSide(
      //   color: Colors.red,
      //   width: 0.4.w,
      // ),
      );

  static final TextStyle _inputTextStyle =
      TextStyle(color: const Color(0xff333333), fontSize: 17.sp);
  static final TextStyle _hintStyle =
      TextStyle(color: const Color(0xFFCCCCCC), fontSize: 17.sp);

  late bool _obscureText;

  @override
  void initState() {
    _obscureText = widget.obscureText;
    _enabledBorder = OutlineInputBorder(
      borderRadius: BorderRadius.circular(widget.radius ?? 21.r),
      borderSide: BorderSide(
        color: const Color(0xFFC8C8CD),
        width: 0.4.w,
      ),
    );
    _focusedBorder = OutlineInputBorder(
        borderRadius: BorderRadius.circular(widget.radius ?? 21.r),
        borderSide: BorderSide.none
        // borderSide: BorderSide(
        //   color: Colors.red,
        //   width: 0.4.w,
        // ),
        );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    InputDecoration decoration;
    var contentPadding = EdgeInsets.only(top: 6.h, left: 24.w, right: 10.w);

    ///前缀图标
    BoxConstraints? prefixIconConstraints;
    Widget? prefixIcon;
    if (widget.prefixImage != null) {
      contentPadding = EdgeInsets.only(top: 6.h, left: 10.w, right: 10.w);
      prefixIconConstraints = BoxConstraints(maxWidth: 40.w);
      prefixIcon = Center(
        child: Image(
          image: widget.prefixImage!,
          width: widget.iconSize.w,
          height: widget.iconSize.h,
        ),
      );
    }

    ///后缀图标
    var suffixIconConstraints = BoxConstraints(maxWidth: 40.w);
    Widget? suffixIcon = _ClearButton(
      focusNode: widget.focusNode,
      controller: widget.controller,
      onClear: widget.onClear, // 传递 onClear 参数
    );
    if (widget.obscureText) {
      suffixIconConstraints = BoxConstraints(maxWidth: 40.w * 2);
      suffixIcon = Row(
        children: [
          const Spacer(),
          SizedBox(
            width: 40.w,
            child: MyTextFieldButton(
              focusNode: widget.focusNode,
              child: Center(
                  child: SizedBox(
                width: 16.w,
                height: 14.h,
                child: Image.asset(_obscureText
                    ? 'assets/images/login/pwd_see.png'
                    : 'assets/images/login/pwd_see_not'),
              )),
              onTap: () => setState(() => _obscureText = !_obscureText),
            ),
          ),
          suffixIcon,
        ],
      );
    }

    if (widget.decoration == null) {
      decoration = InputDecoration(
          enabledBorder: _enabledBorder,
          focusedBorder: _focusedBorder,
          prefixIconConstraints: prefixIconConstraints,
          prefixIcon: prefixIcon,
          suffixIcon: suffixIcon,
          suffixIconConstraints: suffixIconConstraints,
          contentPadding: contentPadding,
          filled: true,
          fillColor: Colors.white);
    } else {
      var copy = widget.decoration!;
      decoration = InputDecoration(
          icon: copy.icon,
          label: copy.label,
          labelText: copy.labelText,
          labelStyle: copy.labelStyle,
          floatingLabelStyle: copy.floatingLabelStyle,
          helperText: copy.helperText,
          helperStyle: copy.helperStyle,
          helperMaxLines: copy.helperMaxLines,
          hintText: copy.hintText,
          hintStyle: copy.hintStyle ?? _hintStyle,
          hintTextDirection: copy.hintTextDirection,
          hintMaxLines: copy.hintMaxLines,
          errorText: copy.errorText,
          errorStyle: copy.errorStyle,
          errorMaxLines: copy.errorMaxLines,
          floatingLabelBehavior: copy.floatingLabelBehavior,
          isCollapsed: copy.isCollapsed,
          isDense: copy.isDense,
          contentPadding: contentPadding,
          prefixIcon: prefixIcon,
          prefix: copy.prefix,
          prefixText: copy.prefixText,
          prefixIconConstraints: prefixIconConstraints,
          prefixStyle: copy.prefixStyle,
          suffixIcon: suffixIcon,
          suffix: copy.suffix,
          suffixText: copy.suffixText,
          suffixStyle: copy.suffixStyle,
          suffixIconConstraints: suffixIconConstraints,
          counter: copy.counter,
          counterText: copy.counterText,
          counterStyle: copy.counterStyle,
          filled: copy.filled ?? true,
          fillColor: copy.fillColor ?? Colors.white,
          focusColor: copy.focusColor,
          hoverColor: copy.hoverColor,
          errorBorder: copy.errorBorder,
          focusedBorder: copy.focusedBorder ?? _focusedBorder,
          focusedErrorBorder: copy.focusedErrorBorder,
          disabledBorder: copy.disabledBorder,
          enabledBorder: copy.enabledBorder ?? _enabledBorder,
          border: copy.border,
          enabled: copy.enabled,
          semanticCounterText: copy.semanticCounterText,
          alignLabelWithHint: copy.alignLabelWithHint,
          constraints: copy.constraints);
    }
    return Container(
        margin: widget.margin,
        height: widget.height ?? 42.h,
        child: TextField(
          inputFormatters: widget.textInputFormatter,
          autofocus: widget.autofocus,
          readOnly: widget.readOnly,
          contextMenuBuilder: (context, editableTextState) {
            return Container(); // 返回空的上下文菜单
          },
          onTap: widget.onTap,
          onEditingComplete: widget.onEditingComplete,
          obscureText: _obscureText,
          controller: widget.controller,
          focusNode: widget.focusNode,
          textInputAction: widget.textInputAction,
          keyboardType: widget.keyboardType,
          style: widget.style ?? _inputTextStyle,
          decoration: decoration,
        ));
  }
}

///MyAuthCodeField
class MyAuthCodeField extends StatefulWidget {
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final InputDecoration? decoration;
  final TextInputAction? textInputAction;
  final double? height;
  final EdgeInsetsGeometry? margin;
  final AssetImage? prefixImage;
  final TextEditingController mobileController;
  final CodeType? codeType;
  final List<TextInputFormatter>? inputFormatters;

  ///构造函数
  const MyAuthCodeField(
      {super.key,
      this.height,
      this.margin,
      this.controller,
      this.focusNode,
      this.textInputAction,
      this.decoration,
      this.prefixImage,
      this.codeType = CodeType.verify,
      this.inputFormatters,
      required this.mobileController});

  @override
  State<StatefulWidget> createState() => _MyAuthCodeFieldState();
}

class _MyAuthCodeFieldState extends State<MyAuthCodeField> {
  @override
  Widget build(BuildContext context) {
    InputDecoration decoration;
    var contentPadding = EdgeInsets.only(top: 6.h, left: 24.w, right: 0.w);

    ///前缀图标
    BoxConstraints? prefixIconConstraints;
    Widget? prefixIcon;
    if (widget.prefixImage != null) {
      contentPadding = EdgeInsets.only(top: 6.h, left: 10.w, right: 0.w);
      prefixIconConstraints = BoxConstraints(maxWidth: 40.w);
      prefixIcon = Center(
        child: Image(
          image: widget.prefixImage!,
          width: 24.w,
          height: 24.h,
        ),
      );
    }

    ///后缀图标
    var suffixIconConstraints = BoxConstraints(maxWidth: 120.w);
    var suffixIcon = Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        _ClearButton(
          focusNode: widget.focusNode,
          controller: widget.controller,
        ),
        _AuthCodeButton(
          focusNode: widget.focusNode,
          codeType: widget.codeType,
          mobileController: widget.mobileController,
        )
      ],
    );
    if (widget.decoration == null) {
      decoration = InputDecoration(
          enabledBorder: _MyTextFieldState._enabledBorder,
          focusedBorder: _MyTextFieldState._focusedBorder,
          prefixIconConstraints: prefixIconConstraints,
          prefixIcon: prefixIcon,
          suffixIcon: suffixIcon,
          suffixIconConstraints: suffixIconConstraints,
          contentPadding: contentPadding,
          filled: true,
          fillColor: Colors.white);
    } else {
      var copy = widget.decoration!;
      decoration = InputDecoration(
          icon: copy.icon,
          label: copy.label,
          labelText: copy.labelText,
          labelStyle: copy.labelStyle,
          floatingLabelStyle: copy.floatingLabelStyle,
          helperText: copy.helperText,
          helperStyle: copy.helperStyle,
          helperMaxLines: copy.helperMaxLines,
          hintText: copy.hintText,
          hintStyle: _MyTextFieldState._hintStyle,
          hintTextDirection: copy.hintTextDirection,
          hintMaxLines: copy.hintMaxLines,
          errorText: copy.errorText,
          errorStyle: copy.errorStyle,
          errorMaxLines: copy.errorMaxLines,
          floatingLabelBehavior: copy.floatingLabelBehavior,
          isCollapsed: copy.isCollapsed,
          isDense: copy.isDense,
          contentPadding: contentPadding,
          prefixIcon: prefixIcon,
          prefix: copy.prefix,
          prefixText: copy.prefixText,
          prefixIconConstraints: prefixIconConstraints,
          prefixStyle: copy.prefixStyle,
          suffixIcon: suffixIcon,
          suffix: copy.suffix,
          suffixText: copy.suffixText,
          suffixStyle: copy.suffixStyle,
          suffixIconConstraints: suffixIconConstraints,
          counter: copy.counter,
          counterText: copy.counterText,
          counterStyle: copy.counterStyle,
          filled: true,
          fillColor: Colors.white,
          focusColor: copy.focusColor,
          hoverColor: copy.hoverColor,
          errorBorder: copy.errorBorder,
          focusedBorder: copy.focusedBorder != InputBorder.none
              ? copy.focusedBorder
              : _MyTextFieldState._focusedBorder,
          focusedErrorBorder: copy.focusedErrorBorder,
          disabledBorder: copy.disabledBorder,
          enabledBorder: copy.enabledBorder != InputBorder.none
              ? copy.enabledBorder
              : _MyTextFieldState._enabledBorder,
          border: copy.border,
          enabled: copy.enabled,
          semanticCounterText: copy.semanticCounterText,
          alignLabelWithHint: copy.alignLabelWithHint,
          constraints: copy.constraints);
    }
    return Container(
        margin: widget.margin,
        height: widget.height ?? 42.h,
        child: TextField(
          obscureText: false,
          controller: widget.controller,
          focusNode: widget.focusNode,
          contextMenuBuilder: (context, editableTextState) {
            return Container(); // ios返回空的上下文菜单
          },
          inputFormatters: widget.inputFormatters,
          textInputAction: widget.textInputAction,
          keyboardType: TextInputType.number,
          style: _MyTextFieldState._inputTextStyle,
          textDirection: TextDirection.ltr,
          textAlign: TextAlign.end,
          decoration: decoration,
        ));
  }
}

///MyTextFieldButton
class MyTextFieldButton extends StatelessWidget {
  final FocusNode? focusNode;
  final GestureTapCallback? onTap;
  final Widget child;

  ///构造函数
  const MyTextFieldButton(
      {Key? key, required this.child, this.focusNode, this.onTap})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (focusNode != null && !focusNode!.hasFocus) {
          focusNode!.canRequestFocus = false;
          Future.delayed(const Duration(milliseconds: 200),
              () => focusNode!.canRequestFocus = true);
        }
        if (onTap != null) onTap!();
      },
      child: child,
    );
  }
}

///_ClearButton
class _ClearButton extends StatefulWidget {
  final FocusNode? focusNode;
  final TextEditingController? controller;
  final VoidCallback? onClear; // 新增 onClear 参数

  const _ClearButton({Key? key, this.focusNode, this.controller, this.onClear})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => _ClearButtonState();
}

class _ClearButtonState extends State<_ClearButton> {
  ///TextEditingController
  TextEditingController? get controller => widget.controller;

  ///是否显示
  bool _show = true;

  @override
  void initState() {
    if (controller != null) {
      _show = controller!.text.isNotEmpty;
      controller!.addListener(_watch);
    }
    super.initState();
  }

  _watch() {
    if (!mounted) return;
    setState(() {
      _show = controller!.text.isNotEmpty;
    });
  }

  @override
  void dispose() {
    if (controller != null) {
      controller!.removeListener(_watch);
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Offstage(
      offstage: !_show,
      child: MyTextFieldButton(
        focusNode: widget.focusNode,
        child: SizedBox(
          width: 40.w,
          child: Center(
            child: SizedBox(
              width: 14.w,
              height: 14.h,
              child: Image.asset('assets/images/com_input_clear.png'),
            ),
          ),
        ),
        onTap: () {
          if (controller == null) return;
          controller!.clear();
          if (widget.onClear != null) widget.onClear!(); // 调用 onClear 回调
        },
      ),
    );
  }
}

//验证码类型
enum CodeType { wechatBind, verify }

///_AUthCodeButton
class _AuthCodeButton extends StatefulWidget {
  final TextEditingController mobileController;
  final FocusNode? focusNode;
  final CodeType? codeType;

  ///构造函数
  const _AuthCodeButton(
      {Key? key,
      required this.mobileController,
      this.focusNode,
      this.codeType = CodeType.verify})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => _AuthCodeButtonState();
}

class _AuthCodeButtonState extends State<_AuthCodeButton> {
  //BMTikTokController? _tikTokController;
  TextEditingController get _mobileController => widget.mobileController;

  int _countDownInSec = 0;
  int _expire = 0;
  bool _buttonEnable = true;

  @override
  void initState() {
    _buttonEnable = _checkMobile();
    _mobileController.addListener(_watch);
    _init();
    super.initState();
  }

  ///验证手机号码是否正确
  bool _checkMobile() {
    ///简单验证
    return _mobileController.text.length == 11;
  }

  ///大陆手机号码11位数，匹配格式：前三位固定格式+后8位任意数
  /// 此方法中前三位格式有：
  /// 13+任意数 * 15+除4的任意数 * 18+除1和4的任意数 * 17+除9的任意数 * 147
  // static bool isChinaPhoneLegal(String str) {
  //   return new RegExp(
  //           '^((13[0-9])|(15[^4])|(166)|(147)|(145)|(17[0-8])|(18[0-9])|(19[5-9]))\\d{8}\$')
  //       .hasMatch(str);
  // }

  ///监听数据变化
  _watch() {
    if (_countDownInSec != 0) return;
    var enable = _checkMobile();
    if (_buttonEnable == enable) return;
    _buttonEnable = enable;
    if (mounted) {
      setState(() {});
    }
  }

  ///初始化
  _init() async {
    // var countDownInSec = await MyAuthCodeService().getCountDownInSec();
    // if (countDownInSec == 0) return;
    // _countDownInSec = countDownInSec;
    // _expire = _countDownInSec + DateTime.now().millisecondsSinceEpoch ~/ 1000;
    // //_tikTokController=BMTikTokController();
    // _buttonEnable = false;
    // setState(() {});
  }

  @override
  void dispose() {
    /*
    if(_tikTokController!=null){
      _tikTokController!.dispose();
    }
     */
    _mobileController.removeListener(_watch);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Widget child;
    if (_countDownInSec == 0) {
      child = Text(
        'get_verification'.tr,
        style: TextStyle(color: Colors.white, fontSize: 17.sp, height: 1.2),
      );
    } else {
      child = YSTikTok.countDown(
          deathInSec: _expire,
          //controller: _tikTokController,
          builder: (BuildContext context, DateTime? death, Duration duration) {
            var inSeconds = duration.inSeconds;
            if (inSeconds <= 0) {
              //_tikTokController!.dispose();
              //_tikTokController=null;
              _countDownInSec = 0;
              _buttonEnable = _checkMobile();
              SchedulerBinding.instance!.addPostFrameCallback((_) {
                setState(() {});
              });
            }
            return Text(
              "$inSeconds s",
              style: TextStyle(color: Colors.white, fontSize: 14.sp),
            );
          });
    }
    return MyTextFieldButton(
      focusNode: widget.focusNode,
      child: SizedBox(
          width: 76.w,
          child: Container(
            height: 34.h,
            // padding: EdgeInsets.symmetric(vertical: 4.h),
            alignment: Alignment.center,
            decoration: BoxDecoration(
                color: _buttonEnable
                    ? Theme.of(context).primaryColor
                    : Colors.grey,
                borderRadius: BorderRadius.all(Radius.circular(3.r))),
            child: child,
          )),
      onTap: _onTap,
    );
  }

  ///点击事件
  _onTap() async {
    if (!_buttonEnable) return;
    if (widget.codeType == CodeType.verify) {
      // smsAuthCode(_mobileController.text); //发送验证码，不验证是否成功
    } else {
      // smsWechatBindCode(_mobileController.text);
    }

    // try {
    //   var result = await smsLoginCode(_mobileController.text);
    //   if (!result) return;
    // } catch (e) {
    //   print("-=-=-=-=-=-=发送失败：" + e.toString());
    //   return;
    // }
    _buttonEnable = false;
    _countDownInSec = 60;
    _expire = _countDownInSec + DateTime.now().millisecondsSinceEpoch ~/ 1000;
    //_tikTokController=BMTikTokController();
    CustomToast.showTextToast("sent".tr);
    if (mounted) {
      setState(() {});
    }
  }
}
