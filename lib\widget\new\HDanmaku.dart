import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_starry_sky_box/base/styles/HTheme.dart';

class HDanmaku extends StatefulWidget {
  @override
  _HDanmakuState createState() => _HDanmakuState();
}

class _HDanmakuState extends State<HDanmaku> with TickerProviderStateMixin {
  final List<_DanmakuItem> _items = [

  ];
  final Random _random = Random();

  void _addDanmaku(String text) {
    final controller = AnimationController(
      duration: Duration(seconds: 6),
      vsync: this,
    );

    final animation = Tween<Offset>(
      begin: Offset(1.0, 0.0),  // 从右边开始
      end: Offset(-1.0, 0.0),  // 到左边结束
    ).animate(controller);

    final track = _random.nextInt(6); // 轨道随机分配

    _items.add(_DanmakuItem(
      text: text,
      animation: animation,
      controller: controller,
      track: track,
    ));

    controller.forward();

    controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          _items.removeWhere((item) => item.controller == controller);
        });
        controller.dispose();
      }
    });

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // 弹幕层
        ..._items.map((item) => AnimatedBuilder(
          animation: item.animation,
          builder: (context, child) {
            return Positioned(
              top: item.track * 30.0 + 20,
              left: MediaQuery.of(context).size.width * item.animation.value.dx,
              child: Container(
                decoration: FltBox.box(
                  bgColor: Colors.black12,radius: 20
                ),
                padding: EdgeInsets.all(3),
                child: Center(
                  child: Text(
                    item.text,
                    style: TextStyle(color: Colors.white, fontSize: 16,fontWeight: FontWeight.w700),
                  ),
                ),
              ),
            );
          },
        )),
        // 控制按钮
        Positioned(
          bottom: 20,
          left: 20,
          child: ElevatedButton(
            onPressed: () => _addDanmaku('Jack_T 成功邀请 ${_random.nextInt(100)}'),
            child: Text('发注册信息'),
          ),
        ),
      ],
    );
  }
}

class _DanmakuItem {
  final String text;
  final Animation<Offset> animation;
  final AnimationController controller;
  final int track;

  _DanmakuItem({
    required this.text,
    required this.animation,
    required this.controller,
    required this.track,
  });
}