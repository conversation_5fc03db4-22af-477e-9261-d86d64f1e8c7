import 'package:flutter/material.dart';
import 'package:flutter_starry_sky_box/base/styles/HTheme.dart';
import 'package:flutter_starry_sky_box/widget/screenutil/custom_screenutil.dart';
import 'package:get/get.dart';

class HSearch extends StatefulWidget {
  final String? hintText;
  final Function(String)? onChanged;
  final Color? bgColor;

  const HSearch({super.key, this.hintText, this.onChanged,this.bgColor});

  @override
  State<StatefulWidget> createState() => _HSearch();
}

class _HSearch extends State<HSearch> {
  @override
  Widget build(BuildContext context) => LayoutBuilder(builder: (ctx, bc) {
    final hint = widget.hintText ?? "search_remark_nickname_or_chat".tr;

    return Container(
      height: bc.maxHeight,
      width: bc.maxWidth,
      decoration: FltBox.box(bgColor: widget.bgColor??Colors.white, radius: 12),
      padding: EdgeInsets.symmetric(vertical: 3, horizontal: 12),
      margin: EdgeInsets.symmetric(vertical: 3, horizontal: 16),
      child: Row(
        children: [
          Icon(
            Icons.search,
            size: 20,
          ),
          12.wbox,
          Expanded(
            child: TextField(
              onChanged: widget.onChanged,
              style: const TextStyle(fontSize: 12),
              maxLines: 1, // 强制一行
              decoration: InputDecoration(
                hintText: hint,
                hintStyle: FltTs.ts(size: 12, height: 1.2),
                border: InputBorder.none,
                isCollapsed: true,          // 去掉默认多余的 padding
                contentPadding: EdgeInsets.zero, // 不要额外的 vertical padding
              ),
            )

          )
        ],
      ),
    );
  });
}
