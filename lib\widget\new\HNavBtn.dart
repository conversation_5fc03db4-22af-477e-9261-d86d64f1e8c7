import 'package:flutter/material.dart';
import 'package:flutter_starry_sky_box/base/styles/HTheme.dart';
import 'package:flutter_starry_sky_box/widget/screenutil/custom_screenutil.dart';

class HNavBtn extends StatelessWidget {
  // 标题 副标题
  final (String, String) t_sub;

  // 图片
  final (String? path, double? maxHeight) img;

  // 背景颜色、文字颜色
  final (List<Color>, Color) bg_tg;
  Function()? onTap;

  final double titleFontSize;

  final double imageOverflowHeight;

  HNavBtn(
      {super.key,
      this.t_sub = ("标题", "sub 标题"),
      this.img = (null, null),
      this.bg_tg = (const [Color(0xFFF6F6F6), Color(0xFFF6F6F6)], Colors.black),
      this.titleFontSize = 14,
      this.imageOverflowHeight = 0,
      this.onTap});

  @override
  Widget build(BuildContext context) => LayoutBuilder(
        builder: (ctx, bc) {
          return InkWell(
            onTap: onTap,
            child: Stack(
              alignment: Alignment.bottomRight,
              children: [
                Container(
                  height: bc.maxHeight - imageOverflowHeight,
                  width: bc.maxWidth,
                  decoration: FltBox.box(gradient_colors: bg_tg.$1, radius: 13),
                ),
                img.$1 == null
                    ? SizedBox()
                    : imageOverflowHeight > 0
                        ? Image.asset(
                            "assets/images/png/chat/${img.$1 ?? ""}.png",
                            fit: BoxFit.contain,
                            height: img?.$2 ?? bc.maxHeight,
                            scale: 146 / 230,
                          )
                        : Align(
                            alignment: Alignment.centerRight,
                            child: Padding(
                              padding: EdgeInsets.only(right: 8),
                              child: Image.asset(
                                "assets/images/png/chat/${img.$1 ?? ""}.png",
                                fit: BoxFit.contain,
                                height: img?.$2 ?? bc.maxHeight,
                                scale: 146 / 230,
                              ),
                            ),
                          ),
                Container(
                  height: bc.maxHeight - imageOverflowHeight,
                  width: bc.maxWidth,
                  padding: EdgeInsets.all(8),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        t_sub.$1,
                        style: FltTs.ts(
                            size: titleFontSize, color: bg_tg.$2, weight: 6),
                      ),
                      6.hbox,
                      Text(
                        t_sub.$2,
                        style: FltTs.ts(size: 10, color: bg_tg.$2, weight: 3),
                      )
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      );
}
