import 'package:flutter/material.dart';
import 'package:flutter_starry_sky_box/base/mixins/SwitchMixin.dart';
import 'package:flutter_starry_sky_box/widget/h/HTag.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_stateless_widget.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_base.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_state.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/view_models/tui_conversation_view_model.dart';
import 'package:tencent_cloud_chat_uikit/data_services/services_locatar.dart';

class HMenuWidgets extends StatefulWidget {
  final List<String> children;
  final Function(int index) onSelectedIndex;

  const HMenuWidgets(
      {super.key, required this.children, required this.onSelectedIndex});

  @override
  State<StatefulWidget> createState() => _HMenuWidgets();
}

class _HMenuWidgets extends TIMUIKitState<HMenuWidgets> with SelectedMixin {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    final conversationModel = serviceLocator<TUIConversationViewModel>();
    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisAlignment: MainAxisAlignment.start,
      children: widget.children.indexed.map((e) {
        final (index, _item) = e;
        return InkWell(
          onTap: () {
            setState(() {
              mixinIndex = index;
            });
            widget.onSelectedIndex(index);
          },
          child: HTag(
            text: _item,
            unTotal: ["full".tr, "no_read".tr].contains(_item) &&
                    conversationModel.totalUnReadCount > 0
                ? conversationModel.totalUnReadCount
                : null,
            show: mixinIndex == index,
          ),
        );
      }).toList(),
    );
  }
}
