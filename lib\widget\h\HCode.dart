import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class HCode extends StatefulWidget {
  final int length;
  final TextStyle textStyle;
  final BoxDecoration boxDecoration;
  final void Function(String)? onCompleted;

  const HCode({
    Key? key,
    this.length = 6,
    this.textStyle = const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
    this.boxDecoration = const BoxDecoration(
      border: Border.fromBorderSide(BorderSide(color: Colors.transparent)),
      borderRadius: BorderRadius.all(Radius.circular(6)),
      color: Colors.black12
    ),
    this.onCompleted,
  }) : super(key: key);

  @override
  _HCodeState createState() => _HCodeState();
}

class _HCodeState extends State<HCode> {
  late List<String> codes;
  late FocusNode focusNode;

  @override
  void initState() {
    super.initState();
    codes = List.filled(widget.length, '');
    focusNode = FocusNode()
      ..addListener(() {
        if (focusNode.hasFocus == false && codes.any((code) => code.isNotEmpty)) {
          // Handle if you need to do something when losing focus
        }
      });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      focusNode.requestFocus();
    });
  }

  void handleKey(RawKeyEvent event) {
    if (event is RawKeyDownEvent) {
      if (event.logicalKey.keyLabel.length == 1 &&
          RegExp(r'[0-9]').hasMatch(event.logicalKey.keyLabel)) {
        for (int i = 0; i < codes.length; i++) {
          if (codes[i].isEmpty) {
            setState(() {
              codes[i] = event.logicalKey.keyLabel;
            });
            if (i == codes.length - 1 && widget.onCompleted != null) {
              widget.onCompleted!(codes.join()); // All filled
            }
            break;
          }
        }
      } else if (event.logicalKey == LogicalKeyboardKey.backspace) {
        for (int i = codes.length - 1; i >= 0; i--) {
          if (codes[i].isNotEmpty) {
            setState(() {
              codes[i] = '';
            });
            break;
          }
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return RawKeyboardListener(
      focusNode: focusNode,
      onKey: handleKey,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(widget.length, (index) {
          return Container(
            margin: EdgeInsets.symmetric(horizontal: 5),
            width: 50, height: 50,
            alignment: Alignment.center,
            decoration: widget.boxDecoration,
            child: Text(
              codes[index],
              style: widget.textStyle,
            ),
          );
        }),
      ),
    );
  }
}