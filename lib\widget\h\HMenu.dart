
import 'package:flutter/material.dart';
import 'package:flutter_starry_sky_box/base/styles/HTheme.dart';

class HMenu extends StatefulWidget {
  final List<String> titles;
  final int initIndex;
  Function(int index)? call;
  HMenu({super.key, required this.titles, this.initIndex = 0, this.call});
  @override
  State<StatefulWidget> createState() => _HMenu();
}

class _HMenu extends State<HMenu> {

  late int _selected = widget.initIndex;
  @override
  void didUpdateWidget(covariant HMenu oldWidget) {
    if (oldWidget.initIndex != widget.initIndex)
      setState(() { _selected = widget.initIndex; });
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) => Row(
    crossAxisAlignment: CrossAxisAlignment.end,
    mainAxisAlignment: MainAxisAlignment.start,
    children: widget.titles.indexed.map((e){
      final (index,_item) = e;
      return InkWell(
        onTap: () {
          if (widget.call!=null) widget.call!(index);
          setState(() { _selected = index; });
        },
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 6),
          child: AnimatedDefaultTextStyle(
            duration: Duration(milliseconds: 250),
            curve: Curves.easeInOut,
            style: FltTs.ts(
              color: Colors.black.withOpacity(_selected == index ? 1 : 0.5),
              weight: _selected == index ? 7 : 6,
              size: _selected == index ? 26 : 20,
            ),
            child: Text(_item),
          ),
        ),
      );
    }).toList(),
  );
}