import 'package:flutter/material.dart';
import 'package:flutter_starry_sky_box/base/styles/HTheme.dart';
import 'package:flutter_starry_sky_box/global.dart';
import 'package:flutter_starry_sky_box/res/rc.dart';
import 'package:flutter_starry_sky_box/widget/screenutil/custom_screenutil.dart';

class HBtn extends StatelessWidget {
  static final Tuple<Color, Color> defultColors =
      Tuple<Color, Color>(t1: rgba(242, 150, 0, 1), t2: Colors.white);
  final Tuple<Color, Color> bg_text_color;
  final String text;
  final double? textSize;
  final double width;
  final double height;
  final double radius;
  final bool enable;
  final bool auto;
  final int? weight;
  final Color? boderColor;
  final double? boderWidth;
  final Widget? icon;
  final EdgeInsetsGeometry? margin;
  final Function()? ontap;
  final Function()? onDouble;

  HBtn(this.text,
      {super.key,
      this.bg_text_color =
          const Tuple<Color, Color>(t1: Color(0xffF29600), t2: Colors.white),
      this.ontap,
      this.onDouble,
      this.auto = false,
      this.width = 200,
      this.height = 40,
      this.radius = 6,
      this.textSize,
      this.boderColor,
      this.boderWidth,
      this.weight,
      this.margin,
      this.enable = true,
      this.icon});

  @override
  Widget build(BuildContext context) => InkWell(
        onTap: enable
            ? () {
                if (ontap != null) ontap!();
              }
            : null,
        onDoubleTap: enable
            ? () {
                if (onDouble != null) onDouble!();
              }
            : null,
        child: Container(
          width: auto ? null : width,
          height: height,
          margin: margin,
          decoration: FltBox.box(
            bgColor: enable ? bg_text_color.t1 : RC.color6A6A6A,
            radius: radius,
            border_color_width: Tuple(
                t1: boderColor ?? Colors.transparent, t2: boderWidth ?? 0.3),
          ),
          child: Center(
            child: FittedBox(
              fit: BoxFit.cover,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    if (icon != null) ...[icon!, 8.wbox],
                    Text(
                      text,
                      style: FltTs.ts(
                          color: bg_text_color.t2,
                          size: textSize ?? 15.sp,
                          height: 1.2,
                          weight: weight ?? 3),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      );
}
