/*
 * @Author: your name
 * @Date: 2025-06-18 09:35:58
 * @LastEditTime: 2025-06-19 08:54:24
 * @LastEditors: your name
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /flt_ship/lib/widget/h/HTag.dart
 */
import 'package:flutter/material.dart';
import 'package:flutter_starry_sky_box/base/styles/HTheme.dart';
import 'package:flutter_starry_sky_box/res/rc.dart';
import 'package:flutter_starry_sky_box/widget/screenutil/custom_screenutil.dart';

class HTag extends StatelessWidget {
  final String text;
  final bool show;
  final double radius;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Decoration? decoration;
  final TextStyle? titleStyle;
  final double? width;
  final double? height;
  final int? unTotal;
  final bool isArrow;

  HTag(
      {required this.text,
      this.show = true,
      this.radius = 50,
      this.padding,
      this.margin,
      this.decoration,
      this.titleStyle,
      this.width,
      this.height,
      this.isArrow = false,
      this.unTotal});

  @override
  Widget build(BuildContext context) => Container(
        width: width,
        height: height,
        padding:
            padding ?? EdgeInsets.symmetric(vertical: 10.h, horizontal: 12.w),
        margin: margin ?? EdgeInsets.symmetric(vertical: 3, horizontal: 4.w),
        decoration: decoration ??
            FltBox.box(
                bgColor: show ? rgba(217, 253, 211, 1) : rgba(244, 244, 244, 1),
                radius: radius),
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                unTotal != null ? '$text $unTotal' : text,
                style: titleStyle ??
                    FltTs.ts(
                      size: 11,
                      color:
                          show ? rgba(21, 96, 62, 1) : rgba(106, 108, 108, 1),
                      weight: show ? 4 : 3,
                    ),
              ),
              if(isArrow)
              Icon(Icons.arrow_drop_down, color: RC.colorACACAC,)
            ],
          ),
        ),
      );
}
