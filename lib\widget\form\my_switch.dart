import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_starry_sky_box/res/rc.dart';

class MySwitchController extends ValueNotifier<bool> {
  MySwitchController([bool value = false]) : super(value);
}

class MySwitch extends StatefulWidget {
  final MySwitchController? controller;
  final bool isChange;
  final bool enabled;

  const MySwitch(
      {Key? key, this.controller, this.isChange = true, this.enabled = true})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => _MySwitchState();
}

class _MySwitchState extends State<MySwitch> {
  late MySwitchController _controller;
  late bool _enabled;

  @override
  void initState() {
    _controller = widget.controller ?? MySwitchController();
    _enabled = widget.enabled;
    _controller.addListener(_whtch);
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    _controller.removeListener(_whtch);
  }

  _whtch() {
    if (mounted) setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoSwitch(
      activeColor: RC.color34C759,
      value: _controller.value,
      onChanged: (v) {
        if (!_enabled) return;
        _controller.value = v;
        if (!mounted) return;
        setState(() {});
      },
    );
  }
}
