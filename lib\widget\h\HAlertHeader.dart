import 'package:flutter/material.dart';
import 'package:flutter_starry_sky_box/base/styles/HTheme.dart';

class H<PERSON><PERSON>tHeader extends StatelessWidget {

  final (Widget l, String? t, String? r) l_t_r;

  HAlertHeader({super.key, this.l_t_r = (const Text("取消"),null,"确定")});

  @override
  Widget build(BuildContext context) => Row(
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children: [
      l_t_r.$1,
      Text(l_t_r.$2 ?? "",style: FltTs.ts(size: 14,weight: 6),),
      Text(l_t_r.$3 ?? "",style: FltTs.ts(size: 13,weight: 5),),
    ],
  );
}